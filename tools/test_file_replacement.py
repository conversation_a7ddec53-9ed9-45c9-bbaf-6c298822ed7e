#!/usr/bin/env python3
"""
Тест разных способов замены файлов с сохранением времени.
"""

import os
import shutil
import subprocess
import tempfile
from pathlib import Path

def test_replacement_methods():
    """Тестируем все способы замены файлов"""
    
    print("🧪 ТЕСТ СПОСОБОВ ЗАМЕНЫ ФАЙЛОВ")
    print("=" * 50)
    
    # Создаем тестовые файлы
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Исходный файл
        original_file = temp_path / "original.txt"
        with open(original_file, 'w') as f:
            f.write("ИСХОДНОЕ СОДЕРЖИМОЕ\nСтрока 2\nСтрока 3")
        
        # Исправленный файл
        fixed_file = temp_path / "fixed.txt"
        with open(fixed_file, 'w') as f:
            f.write("ИСПРАВЛЕННОЕ СОДЕРЖИМОЕ\nНовая строка 2\nНовая строка 3")
        
        # Устанавливаем старое время для оригинала
        old_time = 1600000000  # 2020-09-13
        os.utime(original_file, (old_time, old_time))
        
        print(f"📁 Тестовые файлы созданы в: {temp_path}")
        print(f"📄 Оригинал: {original_file}")
        print(f"🔧 Исправленный: {fixed_file}")
        
        # Получаем исходные метаданные
        orig_stat = os.stat(original_file)
        print(f"\n📊 ИСХОДНЫЕ МЕТАДАННЫЕ:")
        print(f"   Время изменения: {orig_stat.st_mtime}")
        print(f"   Размер: {orig_stat.st_size} байт")
        print(f"   Inode: {orig_stat.st_ino}")
        print(f"   Права: {oct(orig_stat.st_mode)}")
        
        # ТЕСТ 1: Атомарная замена (mv)
        print(f"\n🔄 ТЕСТ 1: Атомарная замена (mv)")
        test1_file = temp_path / "test1.txt"
        shutil.copy2(original_file, test1_file)
        test1_fixed = temp_path / "test1_fixed.txt"
        shutil.copy2(fixed_file, test1_fixed)
        
        # Сохраняем время
        timestamp = os.stat(test1_file).st_mtime
        
        # Заменяем
        shutil.move(test1_fixed, test1_file)
        
        # Восстанавливаем время
        os.utime(test1_file, (timestamp, timestamp))
        
        # Проверяем результат
        new_stat = os.stat(test1_file)
        print(f"   ✅ Время сохранено: {abs(new_stat.st_mtime - timestamp) < 1}")
        print(f"   ⚠️ Inode изменился: {new_stat.st_ino != orig_stat.st_ino}")
        print(f"   ✅ Содержимое: {'ИСПРАВЛЕННОЕ' in open(test1_file).read()}")
        
        # ТЕСТ 2: IN-PLACE замена (cat >)
        print(f"\n🔄 ТЕСТ 2: IN-PLACE замена (cat >)")
        test2_file = temp_path / "test2.txt"
        shutil.copy2(original_file, test2_file)
        
        # Bash команды
        bash_script = f"""
        TIMESTAMP=$(stat -c "%Y" {test2_file})
        cat {fixed_file} > {test2_file}
        touch -d "@$TIMESTAMP" {test2_file}
        """
        
        result = subprocess.run(["bash", "-c", bash_script], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            new_stat = os.stat(test2_file)
            print(f"   ✅ Время сохранено: {abs(new_stat.st_mtime - orig_stat.st_mtime) < 1}")
            print(f"   ✅ Inode сохранен: {new_stat.st_ino == orig_stat.st_ino}")
            print(f"   ✅ Содержимое: {'ИСПРАВЛЕННОЕ' in open(test2_file).read()}")
        else:
            print(f"   ❌ Ошибка: {result.stderr}")
        
        # ТЕСТ 3: Python замена
        print(f"\n🔄 ТЕСТ 3: Python замена")
        test3_file = temp_path / "test3.txt"
        shutil.copy2(original_file, test3_file)
        
        # Сохраняем метаданные
        orig_stat3 = os.stat(test3_file)
        
        # Заменяем содержимое
        with open(fixed_file, 'r') as src, open(test3_file, 'w') as dst:
            dst.write(src.read())
        
        # Восстанавливаем время
        os.utime(test3_file, (orig_stat3.st_atime, orig_stat3.st_mtime))
        
        # Проверяем результат
        new_stat = os.stat(test3_file)
        print(f"   ✅ Время сохранено: {abs(new_stat.st_mtime - orig_stat3.st_mtime) < 1}")
        print(f"   ✅ Inode сохранен: {new_stat.st_ino == orig_stat3.st_ino}")
        print(f"   ✅ Содержимое: {'ИСПРАВЛЕННОЕ' in open(test3_file).read()}")
        
        # ИТОГОВОЕ СРАВНЕНИЕ
        print(f"\n📊 ИТОГОВОЕ СРАВНЕНИЕ:")
        print(f"{'Метод':<20} {'Время':<8} {'Inode':<8} {'Простота':<10}")
        print(f"{'-'*50}")
        print(f"{'1. mv (атомарная)':<20} {'✅':<8} {'❌':<8} {'✅':<10}")
        print(f"{'2. cat > (in-place)':<20} {'✅':<8} {'✅':<8} {'✅':<10}")
        print(f"{'3. Python':<20} {'✅':<8} {'✅':<8} {'⚠️':<10}")
        
        print(f"\n🏆 РЕКОМЕНДАЦИЯ: cat > (in-place) - лучший баланс!")
        
        # Показываем конкретные команды
        print(f"\n💡 КОНКРЕТНЫЕ КОМАНДЫ ДЛЯ ИСПОЛЬЗОВАНИЯ:")
        print(f"# Для обычных файлов:")
        print(f"TIMESTAMP=$(stat -c \"%Y\" оригинал)")
        print(f"cat исправленный > оригинал")
        print(f"touch -d \"@$TIMESTAMP\" оригинал")
        print(f"")
        print(f"# Для файлов в архивах:")
        print(f"TIMESTAMP=$(stat -c \"%Y\" архив.zip)")
        print(f"zip -u архив.zip исправленный_файл")
        print(f"touch -d \"@$TIMESTAMP\" архив.zip")

if __name__ == "__main__":
    test_replacement_methods()
