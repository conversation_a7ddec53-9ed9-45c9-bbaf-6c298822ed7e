#!/usr/bin/env python3
"""
Тест bash подхода для исправления XML файлов.
"""

import json
import os
import shutil
import subprocess
import tempfile
import xml.etree.ElementTree as ET
from pathlib import Path

def test_bash_fix():
    """Тестируем bash подход на реальном файле"""
    
    # Исходные файлы
    source_file = Path("tmp/llm_ed886fa7_5853.fb2")
    
    if not source_file.exists():
        print(f"❌ Файл не найден: {source_file}")
        return False
    
    print("🔧 Тест bash подхода для исправления XML")
    print("=" * 50)
    
    # Создаем временную копию
    with tempfile.NamedTemporaryFile(mode='w', suffix='.fb2', delete=False) as tmp:
        temp_file = Path(tmp.name)
    
    shutil.copy2(source_file, temp_file)
    print(f"📁 Временный файл: {temp_file}")
    
    # Сохраняем время оригинала
    orig_stat = os.stat(source_file)
    print(f"📅 Время оригинала: {orig_stat.st_mtime}")
    
    # Проверяем исходное состояние
    print("\n🔍 Проверка исходного файла:")
    try:
        ET.parse(temp_file)
        print("✅ XML валидный")
    except ET.ParseError as e:
        print(f"❌ XML ошибка: {e}")
    
    # Применяем bash команды для исправления строки 38
    print("\n🔧 Применяем bash исправления:")
    
    # Команда 1: Заменяем строку 38 на исправленную версию
    bash_cmd1 = [
        "sed", "-i", 
        "38s|.*|<p><emphasis>«Иную жизнь в неведомой стране?</emphasis></p><p><emphasis>Ах, в том краю — не то что в этом!</emphasis></p><p><emphasis>Там весело зимой и летом, </emphasis></p><p><emphasis>Повсюду фрукты и цветы</emphasis></p><p><emphasis>Необычайной красоты…»</emphasis></p><p/><p>Роберт Браунинг, «Пестрый дудочник»</p><p>Перевод Марины Бородицкой</p>|",
        str(temp_file)
    ]
    
    # Команда 2: Заменяем строку 37 на исправленную версию  
    bash_cmd2 = [
        "sed", "-i",
        "37s|.*|<p><emphasis>«Зачем мне только обещали</emphasis></p>|",
        str(temp_file)
    ]
    
    try:
        print("   Выполняем: sed для строки 37...")
        result1 = subprocess.run(bash_cmd2, capture_output=True, text=True, check=True)
        print("   ✅ Строка 37 исправлена")
        
        print("   Выполняем: sed для строки 38...")
        result2 = subprocess.run(bash_cmd1, capture_output=True, text=True, check=True)
        print("   ✅ Строка 38 исправлена")
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Ошибка bash команды: {e}")
        print(f"   stderr: {e.stderr}")
        return False
    
    # Проверяем результат
    print("\n✅ Проверка исправленного файла:")
    try:
        ET.parse(temp_file)  # nosec B314 - тестовый код для локальных файлов
        print("✅ XML валидный после исправления!")
        
        # Показываем исправленные строки
        with open(temp_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f"\n📝 Строка 37: {lines[36].strip()}")
            print(f"📝 Строка 38: {lines[37].strip()}")
            
        success = True
        
    except ET.ParseError as e:
        print(f"❌ XML все еще содержит ошибки: {e}")
        success = False
    
    # Восстанавливаем время файла
    if success:
        print(f"\n📅 Восстанавливаем время файла...")
        os.utime(temp_file, (orig_stat.st_atime, orig_stat.st_mtime))
        
        # Проверяем время
        new_stat = os.stat(temp_file)
        if abs(new_stat.st_mtime - orig_stat.st_mtime) < 1:
            print("✅ Время файла восстановлено")
        else:
            print("⚠️ Время файла не совпадает")
    
    print(f"\n🎯 Результат: {'✅ УСПЕХ' if success else '❌ НЕУДАЧА'}")
    print(f"📁 Исправленный файл: {temp_file}")
    
    # Не удаляем временный файл для проверки
    return success

if __name__ == "__main__":
    test_bash_fix()
