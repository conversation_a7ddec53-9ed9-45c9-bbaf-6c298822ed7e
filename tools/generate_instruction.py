#!/usr/bin/env python3
"""
Генератор MD инструкций для исправления XML файлов через bash команды.
"""

import json
from pathlib import Path

def generate_instruction():
    """Генерирует MD инструкцию на основе созданных файлов"""
    
    # Ищем файлы
    prompt_files = list(Path("tmp").glob("llm_*_prompt.json"))
    fb2_files = list(Path("tmp").glob("llm_*.fb2"))
    
    if not prompt_files or not fb2_files:
        print("❌ Не найдены файлы для работы")
        return
    
    prompt_file = prompt_files[-1]
    fb2_file = fb2_files[-1]
    
    # Читаем информацию об ошибке из промпта
    try:
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt_data = json.load(f)
        error_info = prompt_data.get('file_info', {}).get('error', 'XML ошибка')
        error_line = prompt_data.get('file_info', {}).get('error_line', 'неизвестно')
    except:
        error_info = "XML ошибка"
        error_line = "неизвестно"
    
    # Формируем MD инструкцию
    instruction = f"""# ЗАДАЧА: Исправить XML файл через bash команды

## ФАЙЛЫ:
- **Проблемный файл:** `{fb2_file}`
- **Промпт для LLM:** `{prompt_file}`
- **Ошибка:** {error_info} (строка {error_line})

## ИНСТРУКЦИЯ:
1. Отправь содержимое `{prompt_file}` в LLM (ChatGPT/Claude)
2. Получи JSON ответ с полем `bash_commands`
3. Скопируй эту инструкцию + JSON ответ от LLM
4. Отправь мне: **"Примени bash исправления к {fb2_file}"**

## ОЖИДАЕМЫЙ РЕЗУЛЬТАТ:
- Применю bash команды к `{fb2_file}`
- Проверю через `python tools/run_diagnostic_tool.py --input {fb2_file}`
- При успехе покажу количество глав и отсутствие аномалий"""
    
    print(instruction)
    
    # Сохраняем инструкцию в файл для удобства
    instruction_file = Path("tmp") / f"instruction_{prompt_file.stem.split('_')[1]}.md"
    with open(instruction_file, 'w', encoding='utf-8') as f:
        f.write(instruction)
    
    print(f"\n💾 Инструкция сохранена: {instruction_file}")

if __name__ == "__main__":
    generate_instruction()
