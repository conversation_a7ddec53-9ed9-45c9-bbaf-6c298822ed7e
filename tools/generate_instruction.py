#!/usr/bin/env python3
"""
Генератор MD инструкций для исправления XML файлов через bash команды.
"""

import json
from pathlib import Path

def generate_instruction():
    """Генерирует MD инструкцию на основе созданных файлов"""
    
    # Ищем файлы
    prompt_files = list(Path("tmp").glob("llm_*_prompt.json"))
    fb2_files = list(Path("tmp").glob("llm_*.fb2"))
    
    if not prompt_files or not fb2_files:
        print("❌ Не найдены файлы для работы")
        return
    
    prompt_file = prompt_files[-1]
    fb2_file = fb2_files[-1]
    
    # Читаем информацию об ошибке из промпта
    try:
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt_data = json.load(f)
        error_info = prompt_data.get('file_info', {}).get('error', 'XML ошибка')
        error_line = prompt_data.get('file_info', {}).get('error_line', 'неизвестно')
    except:
        error_info = "XML ошибка"
        error_line = "неизвестно"
    
    # Формируем MD инструкцию для меня
    instruction = f"""# ЗАДАЧА: Примени bash исправления к {fb2_file}

## КОНТЕКСТ:
- **Проблемный файл:** `{fb2_file}`
- **Ошибка:** {error_info} (строка {error_line})

## ЧТО МНЕ ДЕЛАТЬ:
1. Применить bash команды из JSON (в конце сообщения) к `{fb2_file}`
2. Проверить результат: `python tools/run_diagnostic_tool.py --input {fb2_file}`
3. Показать количество глав и отсутствие аномалий

---
JSON ОТ LLM БУДЕТ ЗДЕСЬ ↓"""
    
    print(instruction)

if __name__ == "__main__":
    generate_instruction()
