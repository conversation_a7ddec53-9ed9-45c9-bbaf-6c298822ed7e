#!/usr/bin/env python3
"""
🤖 LLM XML Pipeline - ЧИСТЫЙ ВЫВОД для копирования

Запускаешь → копируешь вывод → добавляешь JSON → отправляешь мне
"""

import subprocess
from pathlib import Path

# 🎯 НАСТРОЙКИ - ИЗМЕНИ ЗДЕСЬ ПУТЬ К ПРОБЛЕМНОМУ ФАЙЛУ
SOURCE_PATH = "/mnt/storage/books/zip/zip_searchfloor/6000.zip::5853.fb2"

if __name__ == "__main__":
    # Запускаем основной pipeline БЕЗ вывода
    result = subprocess.run([
        "python3", "tools/llm_xml_pipeline.py"
    ], capture_output=True, text=True, cwd=".")
    
    # Если есть ошибка - показываем только инструкцию
    if result.returncode != 0 or "не требует исправления" not in result.stdout:
        # Запускаем генератор инструкций
        instruction_result = subprocess.run([
            "python3", "tools/generate_instruction.py"
        ], capture_output=True, text=True, cwd=".")
        
        print(instruction_result.stdout.strip())
    else:
        print("Файл не требует исправления")
