#!/usr/bin/env python3
"""
Заменяет содержимое файла в архиве исправленной версией с сохранением timestamp.
Использование: python llm_xml_update_orig.py ed886fa7
"""

import os
import shutil
import sys
import tempfile
import zipfile
from pathlib import Path

def update_original_file(file_id):
    """Заменяет оригинальный файл исправленной версией"""
    
    # Ищем файлы по ID
    tmp_dir = Path("tmp")
    fixed_files = list(tmp_dir.glob(f"llm_{file_id}_*.fb2"))
    
    if not fixed_files:
        print(f"❌ Не найден исправленный файл для ID: {file_id}")
        return False
    
    fixed_file = fixed_files[0]
    
    # Определяем оригинальный файл из SOURCE_PATH в pipeline
    # Ищем в llm_xml_pipeline.py
    pipeline_file = Path("tools/llm_xml_pipeline.py")
    if not pipeline_file.exists():
        print("❌ Не найден tools/llm_xml_pipeline.py")
        return False
    
    with open(pipeline_file, 'r') as f:
        content = f.read()
        
    # Извлекаем SOURCE_PATH
    import re
    match = re.search(r'SOURCE_PATH = "([^"]+)"', content)
    if not match:
        print("❌ Не найден SOURCE_PATH в pipeline")
        return False
    
    source_path = match.group(1)
    
    if "::" not in source_path:
        print("❌ Поддерживаются только файлы в архивах")
        return False
    
    archive_path, file_in_archive = source_path.split("::")
    archive_path = Path(archive_path)
    
    if not archive_path.exists():
        print(f"❌ Архив не найден: {archive_path}")
        return False
    
    print(f"📁 Архив: {archive_path}")
    print(f"📄 Файл в архиве: {file_in_archive}")
    print(f"🔧 Исправленная версия: {fixed_file}")
    
    # Сохраняем timestamp архива
    archive_stat = os.stat(archive_path)
    original_mtime = archive_stat.st_mtime
    
    # Создаем временный архив
    with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_zip:
        temp_zip_path = Path(temp_zip.name)
    
    try:
        # Копируем все файлы кроме заменяемого
        with zipfile.ZipFile(archive_path, 'r') as source_zip:
            with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED) as target_zip:
                
                for item in source_zip.infolist():
                    if item.filename == file_in_archive:
                        # Заменяем на исправленный файл
                        print(f"🔄 Заменяем {item.filename}")
                        with open(fixed_file, 'rb') as fixed:
                            target_zip.writestr(item, fixed.read())
                    else:
                        # Копируем остальные файлы
                        data = source_zip.read(item.filename)
                        target_zip.writestr(item, data)
        
        # Заменяем оригинальный архив
        shutil.move(temp_zip_path, archive_path)
        
        # Восстанавливаем timestamp
        os.utime(archive_path, (archive_stat.st_atime, original_mtime))
        
        print(f"✅ Файл успешно заменен в архиве")
        print(f"📅 Timestamp архива восстановлен: {original_mtime}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        
        # Удаляем временный файл при ошибке
        if temp_zip_path.exists():
            temp_zip_path.unlink()
        
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Использование: python llm_xml_update_orig.py <file_id>")
        print("Пример: python llm_xml_update_orig.py ed886fa7")
        sys.exit(1)
    
    file_id = sys.argv[1]
    success = update_original_file(file_id)
    
    if success:
        print("\n🎉 ГОТОВО! Оригинальный файл обновлен!")
    else:
        print("\n❌ ОШИБКА! Не удалось обновить файл!")
        sys.exit(1)
